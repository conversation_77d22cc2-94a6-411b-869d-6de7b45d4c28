import type { Metadata } from "next";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

export const metadata: Metadata = {
  title: "T恤定制 | POLO衫定做 | 冲锋衣 | 卫衣 | 工作服 | 连帽卫衣 | 马甲 | 棒球服 - YX Uniform",
  description: "YX Sport是专业的团队运动服装，足球及其相关产品的领先品牌公司，出口商，制造商。我们提供广州T恤定制，POLO衫定做，冲锋衣，卫衣，工作服，连帽卫衣，马甲，棒球服等服务。",
  keywords: ["广州T恤定制", "POLO衫定做", "冲锋衣", "卫衣", "工作服", "连帽卫衣", "马甲", "棒球服"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body>
        <Header />
        {children}
        <Footer />
      </body>
    </html>
  );
}