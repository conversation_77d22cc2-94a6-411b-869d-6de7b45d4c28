# YX Uniform Website

This is a clone of the website https://www.yxuniform.com, built with Next.js and Tailwind CSS.

## Running the Website

To run the website, you need to have Node.js and npm installed on your machine. Then, follow these steps:

1.  Install the dependencies:

```bash
npm install
```

2.  Run the development server:

```bash
npm run dev
```

This will start the website on `http://localhost:3000`.

## Replacing SEO Keywords

To replace the SEO keywords, you need to edit the `app/layout.tsx` file. In this file, you will find the `metadata` object. You can change the `title`, `description`, and `keywords` properties to your liking.

```typescript
export const metadata: Metadata = {
  title: "T恤定制 | POLO衫定做 | 冲锋衣 | 卫衣 | 工作服 | 连帽卫衣 | 马甲 | 棒球服 - YX Uniform",
  description: "YX Sport是专业的团队运动服装，足球及其相关产品的领先品牌公司，出口商，制造商。我们提供广州T恤定制，POLO衫定做，冲锋衣，卫衣，工作服，连帽卫衣，马甲，棒球服等服务。",
  keywords: ["广州T恤定制", "POLO衫定做", "冲锋衣", "卫衣", "工作服", "连帽卫衣", "马甲", "棒球服"],
};
```

You can also edit the metadata for each individual page by editing the `metadata` object in the corresponding `page.tsx` file.

## Replacing Homepage Banner Images

To replace the homepage banner images, you need to edit the `data/banner.json` file. This file contains a list of objects, where each object represents a banner image. You can change the `image` property to the path of your new image.

```json
[
  {
    "id": 1,
    "image": "/images/banner1.jpg"
  },
  {
    "id": 2,
    "image": "/images/banner2.jpg"
  },
  {
    "id": 3,
    "image": "/images/banner3.jpg"
  }
]
```

Make sure to place your new images in the `public/images` directory.

## Replacing Product Images and Descriptions

To replace the product images and descriptions, you need to edit the `data/products.json` file. This file contains a list of objects, where each object represents a product. You can change the `name` and `image` properties to your liking.

```json
[
  {
    "id": 1,
    "name": "Product 1",
    "image": "/images/product1.jpg"
  },
  {
    "id": 2,
    "name": "Product 2",
    "image": "/images/product2.jpg"
  },
  // ...
]
```

Make sure to place your new images in the `public/images` directory.