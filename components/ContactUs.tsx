import Image from 'next/image';

const ContactUs = () => {
  return (
    <div>
      <div className="relative h-64">
        <Image
          src="/images/banner2.jpg"
          alt="Contact Us Banner"
          layout="fill"
          objectFit="cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <h1 className="text-white text-4xl font-bold">联系我们</h1>
        </div>
      </div>
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div>
            <h2 className="text-3xl font-bold mb-4">联系方式</h2>
            <p className="text-gray-600 mb-2">地址：广州市白云区永平街道永泰集贤北八巷19号</p>
            <p className="text-gray-600 mb-2">电话：+86 15068103397</p>
            <p className="text-gray-600 mb-2">邮箱：<EMAIL></p>
            <div className="mt-8">
              <h3 className="text-2xl font-bold mb-4">在线留言</h3>
              <form>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-gray-700 font-bold mb-2">姓名</label>
                  <input type="text" id="name" name="name" className="w-full px-3 py-2 border rounded-lg" />
                </div>
                <div className="mb-4">
                  <label htmlFor="email" className="block text-gray-700 font-bold mb-2">邮箱</label>
                  <input type="email" id="email" name="email" className="w-full px-3 py-2 border rounded-lg" />
                </div>
                <div className="mb-4">
                  <label htmlFor="message" className="block text-gray-700 font-bold mb-2">内容</label>
                  <textarea id="message" name="message" rows={5} className="w-full px-3 py-2 border rounded-lg"></textarea>
                </div>
                <button type="submit" className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">提交</button>
              </form>
            </div>
          </div>
          <div>
            <h2 className="text-3xl font-bold mb-4">地理位置</h2>
            <div className="h-96 bg-gray-200 rounded-lg">
              {/* Replace with a map component */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
