import Link from 'next/link';
import news from '@/data/news.json';

const News = () => {
  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">新闻中心</h2>
          <Link href="/news" className="text-gray-600 hover:text-gray-900">
            More &gt;
          </Link>
        </div>
        <ul>
          {news.map((item) => (
            <li key={item.id} className="border-b py-4">
              <Link href={`/news/${item.id}`} className="flex items-center">
                <div className="text-gray-500 text-lg mr-4">{item.date}</div>
                <div className="text-lg hover:text-blue-500">{item.title}</div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default News;