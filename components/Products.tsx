import Image from 'next/image';
import Link from 'next/link';
import products from '@/data/products.json';

const Products = () => {
  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold">产品展示</h2>
          <Link href="/products" className="text-gray-600 hover:text-gray-900">
            More &gt;
          </Link>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6 md:gap-8">
          {products.map((product) => (
            <div key={product.id} className="group">
              <div className="relative w-full overflow-hidden rounded-lg" style={{ paddingTop: '100%' }}>
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  sizes="(min-width: 1024px) 20vw, (min-width: 768px) 25vw, 50vw"
                  className="object-cover transition-transform duration-300 ease-in-out group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                  <h3 className="text-white text-base md:text-lg font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center px-2">
                    {product.name}
                  </h3>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Products;