import Image from 'next/image';
import categories from '@/data/productCategories.json';

const ProductCategories = () => {
  return (
    <div className="bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-8">产品中心</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
          {categories.map((category) => (
            <div key={category.title} className="group">
              <div className="relative w-full overflow-hidden rounded-lg" style={{ paddingTop: '100%' }}>
                <Image
                  src={category.image}
                  alt={category.title}
                  fill
                  sizes="(min-width: 768px) 25vw, 50vw"
                  className="object-cover transition-transform duration-300 ease-in-out group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                  <h3 className="text-white text-lg font-bold text-center px-2">{category.title}</h3>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProductCategories;